import type { UserProfile } from '@/types/auth.ts';

interface CachedProfile {
  profile: UserProfile;
  timestamp: number;
  etag?: string;
}

interface ProfileCompletionStatus {
  userId: string;
  isComplete: boolean;
  timestamp: number;
}

const CACHE_KEY = 'user_profile_cache';
const COMPLETION_STATUS_KEY = 'profile_completion_status';
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes for complete profiles
const INCOMPLETE_CACHE_DURATION = 30 * 1000; // 30 seconds for incomplete profiles
const COMPLETION_STATUS_DURATION = 24 * 60 * 60 * 1000; // 24 hours for completion status

/**
 * Profile cache utility to reduce redundant API calls
 */
export class ProfileCache {
  /**
   * Get cached profile if valid
   */
  static getCachedProfile(userId: string): UserProfile | null {
    try {
      const cached = localStorage.getItem(`${CACHE_KEY}_${userId}`);
      if (!cached) return null;

      const { profile, timestamp, etag }: CachedProfile = JSON.parse(cached);
      const now = Date.now();
      
      // Determine cache duration based on profile completeness
      const isComplete = profile.username && profile.is_verified;
      const maxAge = isComplete ? CACHE_DURATION : INCOMPLETE_CACHE_DURATION;
      
      if (now - timestamp > maxAge) {
        // Cache expired
        localStorage.removeItem(`${CACHE_KEY}_${userId}`);
        return null;
      }

      return profile;
    } catch (error) {
      console.warn('Error reading profile cache:', error);
      return null;
    }
  }

  /**
   * Cache profile data
   */
  static setCachedProfile(profile: UserProfile, etag?: string): void {
    try {
      const cached: CachedProfile = {
        profile,
        timestamp: Date.now(),
        etag
      };
      localStorage.setItem(`${CACHE_KEY}_${profile.id}`, JSON.stringify(cached));
      
      // Also update completion status
      this.setCompletionStatus(profile.id, !!(profile.username && profile.is_verified));
    } catch (error) {
      console.warn('Error caching profile:', error);
    }
  }

  /**
   * Get cached ETag for conditional requests
   */
  static getCachedETag(userId: string): string | null {
    try {
      const cached = localStorage.getItem(`${CACHE_KEY}_${userId}`);
      if (!cached) return null;

      const { etag }: CachedProfile = JSON.parse(cached);
      return etag || null;
    } catch (error) {
      console.warn('Error reading cached ETag:', error);
      return null;
    }
  }

  /**
   * Clear cached profile
   */
  static clearCachedProfile(userId: string): void {
    try {
      localStorage.removeItem(`${CACHE_KEY}_${userId}`);
    } catch (error) {
      console.warn('Error clearing profile cache:', error);
    }
  }

  /**
   * Check if user has completed their profile (cached check)
   */
  static isProfileComplete(userId: string): boolean | null {
    try {
      const cached = localStorage.getItem(`${COMPLETION_STATUS_KEY}_${userId}`);
      if (!cached) return null;

      const { isComplete, timestamp }: ProfileCompletionStatus = JSON.parse(cached);
      const now = Date.now();
      
      if (now - timestamp > COMPLETION_STATUS_DURATION) {
        // Status expired
        localStorage.removeItem(`${COMPLETION_STATUS_KEY}_${userId}`);
        return null;
      }

      return isComplete;
    } catch (error) {
      console.warn('Error reading completion status cache:', error);
      return null;
    }
  }

  /**
   * Set profile completion status
   */
  static setCompletionStatus(userId: string, isComplete: boolean): void {
    try {
      const status: ProfileCompletionStatus = {
        userId,
        isComplete,
        timestamp: Date.now()
      };
      localStorage.setItem(`${COMPLETION_STATUS_KEY}_${userId}`, JSON.stringify(status));
    } catch (error) {
      console.warn('Error caching completion status:', error);
    }
  }

  /**
   * Clear completion status cache
   */
  static clearCompletionStatus(userId: string): void {
    try {
      localStorage.removeItem(`${COMPLETION_STATUS_KEY}_${userId}`);
    } catch (error) {
      console.warn('Error clearing completion status cache:', error);
    }
  }

  /**
   * Fetch profile with caching and conditional requests
   * IMPORTANT: This assumes the API route returns the profile for the currently authenticated user
   */
  static async fetchProfile(expectedUserId: string, retryCount = 0): Promise<UserProfile | null> {
    const MAX_RETRIES = 2;
    const RETRY_DELAY = 1000; // 1 second delay between retries

    // First check cache
    const cachedProfile = this.getCachedProfile(expectedUserId);
    if (cachedProfile) {
      return cachedProfile;
    }

    try {
      // Prepare headers for conditional request
      const headers: HeadersInit = {};
      const cachedETag = this.getCachedETag(expectedUserId);
      if (cachedETag) {
        headers['If-None-Match'] = cachedETag;
      }

      // Add cache-busting for retries to ensure fresh request
      if (retryCount > 0) {
        headers['Cache-Control'] = 'no-cache, no-store, must-revalidate';
        headers['Pragma'] = 'no-cache';
        headers['Expires'] = '0';
      }

      const response = await fetch('/api/auth/profile', { headers });

      if (response.status === 304) {
        // Not modified, return cached version
        return this.getCachedProfile(expectedUserId);
      }

      if (!response.ok) {
        if (response.status === 404) {
          // Profile not found, cache this information briefly
          this.setCompletionStatus(expectedUserId, false);
          return null;
        }
        throw new Error(`Profile fetch failed: ${response.status}`);
      }

      const profile: UserProfile = await response.json();
      const etag = response.headers.get('etag');

      // CRITICAL: Validate that the returned profile matches the expected user ID
      if (profile.id !== expectedUserId) {
        console.error(`Profile ID mismatch! Expected: ${expectedUserId}, Got: ${profile.id}`);

        // Clear all cache immediately to prevent any cross-contamination
        this.clearAllCache();

        // Cache the received profile under its correct user ID
        console.log(`Caching received profile under correct user ID: ${profile.id}`);
        this.setCachedProfile(profile, etag || undefined);

        // If this is a retry, we've likely hit a session sync issue
        if (retryCount < MAX_RETRIES) {
          console.log(`Profile ID mismatch on attempt ${retryCount + 1}. Retrying in ${RETRY_DELAY}ms...`);
          await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
          return this.fetchProfile(expectedUserId, retryCount + 1);
        }

        // Max retries exceeded - the expected user might not have a profile
        console.warn(`Profile ID mismatch after ${MAX_RETRIES + 1} attempts. Expected user ${expectedUserId} may not have a profile.`);
        this.setCompletionStatus(expectedUserId, false);
        return null;
      }

      // Cache the profile
      this.setCachedProfile(profile, etag || undefined);

      return profile;
    } catch (error: any) {
      // For other errors, retry if we haven't exceeded the limit
      if (retryCount < MAX_RETRIES && !error.message?.includes('Profile fetch failed')) {
        console.log(`Retrying profile fetch due to error: ${error.message} (attempt ${retryCount + 1}/${MAX_RETRIES + 1})`);
        await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
        return this.fetchProfile(expectedUserId, retryCount + 1);
      }

      console.error('Error fetching profile:', error);
      throw error;
    }
  }

  /**
   * Clear all cached data for a user
   */
  static clearUserCache(userId: string): void {
    this.clearCachedProfile(userId);
    this.clearCompletionStatus(userId);
  }

  /**
   * Clear all cached data
   */
  static clearAllCache(): void {
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(CACHE_KEY) || key.startsWith(COMPLETION_STATUS_KEY)) {
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.warn('Error clearing all cache:', error);
    }
  }

  /**
   * Handle account switching by clearing all cache and setting new current user
   * This prevents profile data leakage between different user accounts
   */
  static handleAccountSwitch(newUserId: string, previousUserId?: string): void {
    console.log(`[ProfileCache] Account switch detected: ${previousUserId || 'unknown'} -> ${newUserId}`);

    // Clear all cache to prevent data leakage
    this.clearAllCache();

    // Also clear any browser-level cache that might interfere
    try {
      // Clear any stale fetch cache
      if ('caches' in window) {
        caches.keys().then(names => {
          names.forEach(name => {
            if (name.includes('auth') || name.includes('profile')) {
              caches.delete(name);
            }
          });
        });
      }
    } catch (error) {
      console.warn('Error clearing browser caches:', error);
    }

    // Log the switch for debugging
    console.log('[ProfileCache] All cache cleared due to account switch');
  }

  /**
   * Get all cached user IDs (for debugging and validation)
   */
  static getCachedUserIds(): string[] {
    try {
      const keys = Object.keys(localStorage);
      const userIds = keys
        .filter(key => key.startsWith(CACHE_KEY))
        .map(key => key.replace(`${CACHE_KEY}_`, ''))
        .filter(id => id.length > 0);
      return [...new Set(userIds)]; // Remove duplicates
    } catch (error) {
      console.warn('Error getting cached user IDs:', error);
      return [];
    }
  }

  /**
   * Validate cache integrity - check for any profile ID mismatches
   */
  static validateCacheIntegrity(): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];

    try {
      const cachedUserIds = this.getCachedUserIds();

      for (const userId of cachedUserIds) {
        const profile = this.getCachedProfile(userId);
        if (profile && profile.id !== userId) {
          issues.push(`Profile ID mismatch for cache key ${userId}: profile.id = ${profile.id}`);
        }
      }
    } catch (error) {
      issues.push(`Error validating cache: ${error}`);
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  }

  /**
   * Smart profile completion check that avoids API calls when possible
   */
  static async checkProfileCompletion(userId: string): Promise<{
    isComplete: boolean;
    profile?: UserProfile;
    fromCache: boolean;
  }> {
    // First check cached completion status
    const cachedStatus = this.isProfileComplete(userId);
    if (cachedStatus === true) {
      return { isComplete: true, fromCache: true };
    }

    // Check cached profile
    const cachedProfile = this.getCachedProfile(userId);
    if (cachedProfile) {
      const isComplete = !!(cachedProfile.username && cachedProfile.is_verified);
      return {
        isComplete,
        profile: cachedProfile,
        fromCache: true
      };
    }

    // If no cache, fetch from API
    try {
      const profile = await this.fetchProfile(userId);
      const isComplete = !!(profile?.username && profile?.is_verified);
      return {
        isComplete,
        profile: profile || undefined,
        fromCache: false
      };
    } catch (error) {
      // If profile fetch fails (e.g., 404), assume incomplete
      this.setCompletionStatus(userId, false);
      return { isComplete: false, fromCache: false };
    }
  }
}
